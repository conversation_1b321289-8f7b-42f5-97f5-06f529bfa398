from core_app.views import StudentPermission
from rest_framework import generics, serializers
from rest_framework.permissions import IsAuthenticated
from teacher_app.models import Assignment, Examination

from .models import ExamSubmission, Submission
from .serializers import (
    ExamSubmissionCreateSerializer,
    ExamSubmissionSerializer,
    SubmissionCreateSerializer,
    SubmissionSerializer,
)


class StudentAssignmentListView(generics.ListAPIView):
    """List all assignments available to the student"""
    serializer_class = Assignment
    permission_classes = [IsAuthenticated, StudentPermission]

    def get_queryset(self):
        """Filter assignments by student's organizations"""
        user = self.request.user
        # Get all organizations where user has student role
        student_orgs = user.get_role_organizations('student')
        return Assignment.objects.filter(
            organization__in=student_orgs,
            is_active=True
        ).order_by('-created_at')


class StudentExaminationListView(generics.ListAPIView):
    """List all examinations available to the student"""
    serializer_class = Examination
    permission_classes = [IsAuthenticated, StudentPermission]

    def get_queryset(self):
        """Filter examinations by student's organizations"""
        user = self.request.user
        student_orgs = user.get_role_organizations('student')
        return Examination.objects.filter(
            organization__in=student_orgs,
            is_active=True
        ).order_by('-start_time')


class StudentSubmissionListCreateView(generics.ListCreateAPIView):
    """List student's submissions and create new ones"""
    permission_classes = [IsAuthenticated, StudentPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return SubmissionCreateSerializer
        return SubmissionSerializer

    def get_queryset(self):
        """Filter submissions by current student"""
        user = self.request.user
        student_orgs = user.get_role_organizations('student')
        return Submission.objects.filter(
            organization__in=student_orgs,
            student_role__user=user
        ).order_by('-submitted_at')

    def perform_create(self, serializer):
        """Create submission with proper student role assignment"""
        assignment = serializer.validated_data['assignment']
        # Get the user's student role in this organization
        from core_app.models import UserOrganizationRole
        try:
            student_role = UserOrganizationRole.objects.get(
                user=self.request.user,
                organization=assignment.organization,
                role__code='student',
                is_active=True
            )
            serializer.save(student_role=student_role)
        except UserOrganizationRole.DoesNotExist:
            raise serializers.ValidationError(
                "Student role not found for this organization"
            )


class StudentExamSubmissionListCreateView(generics.ListCreateAPIView):
    """List student's exam submissions and create new ones"""
    permission_classes = [IsAuthenticated, StudentPermission]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ExamSubmissionCreateSerializer
        return ExamSubmissionSerializer

    def get_queryset(self):
        """Filter exam submissions by current student"""
        user = self.request.user
        student_orgs = user.get_role_organizations('student')
        return ExamSubmission.objects.filter(
            organization__in=student_orgs,
            student_role__user=user
        ).order_by('-submitted_at')

    def perform_create(self, serializer):
        """Create exam submission with proper student role assignment"""
        examination = serializer.validated_data['examination']
        # Get the user's student role in this organization
        from core_app.models import UserOrganizationRole
        try:
            student_role = UserOrganizationRole.objects.get(
                user=self.request.user,
                organization=examination.organization,
                role__code='student',
                is_active=True
            )
            serializer.save(student_role=student_role)
        except UserOrganizationRole.DoesNotExist:
            raise serializers.ValidationError(
                "Student role not found for this organization"
            )
