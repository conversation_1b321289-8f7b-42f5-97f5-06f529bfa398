"""
Management command to migrate existing users from boolean fields to the new role system.
This command should be run after creating the new models and setting up default roles.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from Ilearnova.models import (
    CustomUser, UserRole, UserOrganizationRole, Organization,
    Teacher, Student, Parent
)


class Command(BaseCommand):
    help = 'Migrate existing users from boolean fields to the new role system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without making changes',
        )
        parser.add_argument(
            '--create-default-org',
            action='store_true',
            help='Create a default organization for users without one',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting migration to role system...')
        )

        dry_run = options['dry_run']
        create_default_org = options['create_default_org']

        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )

        # Get or create default organization if needed
        default_org = None
        if create_default_org:
            default_org, created = Organization.objects.get_or_create(
                code='DEFAULT',
                defaults={
                    'name': 'Default Organization',
                    'description': 'Default organization for migrated users',
                }
            )
            if created and not dry_run:
                self.stdout.write(
                    self.style.SUCCESS(f'Created default organization: {default_org}')
                )

        # Get role objects
        try:
            teacher_role = UserRole.objects.get(code='teacher')
            student_role = UserRole.objects.get(code='student')
            parent_role = UserRole.objects.get(code='parent')
        except UserRole.DoesNotExist as e:
            self.stdout.write(
                self.style.ERROR(
                    f'Required role not found: {e}. '
                    'Please run setup_default_roles command first.'
                )
            )
            return

        migration_stats = {
            'teachers': 0,
            'students': 0,
            'parents': 0,
            'errors': 0,
        }

        with transaction.atomic():
            if dry_run:
                # Create a savepoint for dry run
                savepoint = transaction.savepoint()

            # Migrate Teachers
            self.stdout.write('\nMigrating Teachers...')
            for teacher in Teacher.objects.all():
                try:
                    org = teacher.organization or default_org
                    if not org:
                        self.stdout.write(
                            self.style.ERROR(
                                f'No organization for teacher {teacher.user.username}. '
                                'Use --create-default-org flag.'
                            )
                        )
                        migration_stats['errors'] += 1
                        continue

                    # Check if role assignment already exists
                    if not UserOrganizationRole.objects.filter(
                        user=teacher.user,
                        organization=org,
                        role=teacher_role
                    ).exists():
                        
                        if not dry_run:
                            UserOrganizationRole.objects.create(
                                user=teacher.user,
                                organization=org,
                                role=teacher_role,
                                is_verified=teacher.is_verified,
                                verified_at=timezone.now() if teacher.is_verified else None,
                                additional_data={
                                    'phone_number': teacher.phone_number,
                                    'address': teacher.address,
                                    'migrated_from_legacy': True,
                                }
                            )
                        
                        migration_stats['teachers'] += 1
                        self.stdout.write(f'  ✓ {teacher.user.username}')
                    else:
                        self.stdout.write(f'  - {teacher.user.username} (already exists)')

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'  ✗ Error migrating teacher {teacher.user.username}: {e}')
                    )
                    migration_stats['errors'] += 1

            # Migrate Students
            self.stdout.write('\nMigrating Students...')
            for student in Student.objects.all():
                try:
                    org = student.organization or default_org
                    if not org:
                        self.stdout.write(
                            self.style.ERROR(
                                f'No organization for student {student.user.username}. '
                                'Use --create-default-org flag.'
                            )
                        )
                        migration_stats['errors'] += 1
                        continue

                    # Check if role assignment already exists
                    if not UserOrganizationRole.objects.filter(
                        user=student.user,
                        organization=org,
                        role=student_role
                    ).exists():
                        
                        if not dry_run:
                            UserOrganizationRole.objects.create(
                                user=student.user,
                                organization=org,
                                role=student_role,
                                is_verified=True,  # Students don't require verification
                                verified_at=timezone.now(),
                                additional_data={
                                    'address': student.address,
                                    'birthday': student.birthday.isoformat() if student.birthday else None,
                                    'migrated_from_legacy': True,
                                }
                            )
                        
                        migration_stats['students'] += 1
                        self.stdout.write(f'  ✓ {student.user.username}')
                    else:
                        self.stdout.write(f'  - {student.user.username} (already exists)')

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'  ✗ Error migrating student {student.user.username}: {e}')
                    )
                    migration_stats['errors'] += 1

            # Migrate Parents
            self.stdout.write('\nMigrating Parents...')
            for parent in Parent.objects.all():
                try:
                    org = parent.organization or default_org
                    if not org:
                        self.stdout.write(
                            self.style.ERROR(
                                f'No organization for parent {parent.user.username}. '
                                'Use --create-default-org flag.'
                            )
                        )
                        migration_stats['errors'] += 1
                        continue

                    # Check if role assignment already exists
                    if not UserOrganizationRole.objects.filter(
                        user=parent.user,
                        organization=org,
                        role=parent_role
                    ).exists():
                        
                        if not dry_run:
                            UserOrganizationRole.objects.create(
                                user=parent.user,
                                organization=org,
                                role=parent_role,
                                is_verified=parent.is_verified,
                                verified_at=timezone.now() if parent.is_verified else None,
                                additional_data={
                                    'phone_number': parent.phone_number,
                                    'migrated_from_legacy': True,
                                }
                            )
                        
                        migration_stats['parents'] += 1
                        self.stdout.write(f'  ✓ {parent.user.username}')
                    else:
                        self.stdout.write(f'  - {parent.user.username} (already exists)')

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'  ✗ Error migrating parent {parent.user.username}: {e}')
                    )
                    migration_stats['errors'] += 1

            if dry_run:
                # Rollback the savepoint for dry run
                transaction.savepoint_rollback(savepoint)

        # Print summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write('MIGRATION SUMMARY')
        self.stdout.write('='*50)
        self.stdout.write(f'Teachers migrated: {migration_stats["teachers"]}')
        self.stdout.write(f'Students migrated: {migration_stats["students"]}')
        self.stdout.write(f'Parents migrated: {migration_stats["parents"]}')
        self.stdout.write(f'Errors: {migration_stats["errors"]}')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nDRY RUN COMPLETED - No actual changes were made')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('\nMIGRATION COMPLETED SUCCESSFULLY')
            )
