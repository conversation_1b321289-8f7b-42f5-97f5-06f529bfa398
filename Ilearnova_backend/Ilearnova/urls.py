from django.urls import path
from .views import *

urlpatterns = [
    path('register/teacher/', TeacherRegisterView.as_view()),
    path('register/parent/', ParentRegisterView.as_view()),
    path('register/student/', StudentRegisterView.as_view()),
    path('login/', LoginView.as_view()),
    path('verify/<str:role>/<str:username>/', VerifyUserView.as_view(), name='verify-user'),
    path('profile/', ProfileView.as_view()),
    path('class/', CreateClassView.as_view()),
    path('class_details/<int:class_id>/', ClassesDetailView.as_view()),
    path('enroll/<int:class_id>/', EnrollStudent.as_view(), name='enroll-student'),
    path('subject/<int:class_id>/', SubjectView.as_view(), name='subject'),
    path('topic/<int:subject_id>/', TopicView.as_view(), name='topic'),
    path('assignment/<int:subject_id>/', AssignmentView.as_view(), name='assignment'),
    path('assignment/<int:assignment_id>/submit/', SubmitAssignmentView.as_view(), name='submit-assignment'),
]
