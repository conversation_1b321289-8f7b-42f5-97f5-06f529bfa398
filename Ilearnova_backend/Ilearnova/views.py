from django.contrib.auth import authenticate, get_user_model
from django.core.mail import send_mail
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import generics, status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

from .models import *
from .serializers import *

User = get_user_model()


# Temporarily commented out for migration
# class TeacherRegisterView(generics.CreateAPIView):
#     serializer_class = TeacherRegistrationSerializer
#     permission_classes = [AllowAny]

# class ParentRegisterView(generics.CreateAPIView):
#     serializer_class = ParentRegistrationSerializer
#     permission_classes = [AllowAny]

# class StudentRegisterView(generics.CreateAPIView):
#     serializer_class = StudentRegistrationSerializer
#     permission_classes = [AllowAny]



class LoginView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        username = request.data.get("username")
        password = request.data.get("password")

        user = authenticate(username=username, password=password)

        if user:
            if user.is_teacher and not user.teacher.is_verified:
                return Response({"error": "Teacher not verified yet."}, status=status.HTTP_403_FORBIDDEN)

            if user.is_parent and not user.parent.is_verified:
                return Response({"error": "Parent not verified yet."}, status=status.HTTP_403_FORBIDDEN)

            serializer = TokenObtainPairSerializer(data={"username": username, "password": password})
            try:
                serializer.is_valid(raise_exception=True)
            except Exception:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            return Response(serializer.validated_data, status=status.HTTP_200_OK)

        return Response({"error": "Invalid credentials"}, status=status.HTTP_401_UNAUTHORIZED)

class VerifyUserView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, role, username):
        user = get_object_or_404(User, username=username)
        from_email = settings.DEFAULT_FROM_EMAIL

        if role == "teacher" and hasattr(user, "teacher"):
            user.teacher.is_verified = True
            user.teacher.save()
            send_mail('Verification Complete', 'Your teacher account has been verified.', from_email , [user.email])
            return HttpResponse(f"Teacher {user.username} verified successfully.")
        elif role == "parent" and hasattr(user, "parent"):
            user.parent.is_verified = True
            user.parent.save()
            send_mail('Verification Complete', 'Your parent account has been verified.', from_email, [user.email])
            return HttpResponse(f"Parent {user.username} verified successfully.")
        return HttpResponse("Invalid verification link.", status=400)

class ProfileView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ProfileSerializer

    def get(self, request):

        if not hasattr(request.user, 'student'):
            return Response({"error": "User is not a student."}, status=403)
         
        try:
            student = Student.objects.get(user=request.user)
        except Student.DoesNotExist:
            return Response({"error": "Student profile not found."}, status=404)

        serializer = ProfileSerializer(student)
        return Response(serializer.data)

    def put(self, request):
        if not hasattr(request.user, 'student'):
            return Response({"error": "User is not a student."}, status=403)
        try:
            student = Student.objects.get(user=request.user)
        except Student.DoesNotExist:
            return Response({"error": "Student profile not found."}, status=404)

        serializer = ProfileSerializer(student, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Profile successfully updated", "data": serializer.data}, status=200)
        return Response(serializer.errors, status=400)

class CreateClassView(generics.CreateAPIView):
    serializer_class = ClassesSerialzer
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        classname = request.data.get("classname")

        if Classes.objects.filter(name=classname).exists():
            return Response({"error": "Class name already exists."}, status=status.HTTP_400_BAD_REQUEST)
        if not classname:
            return Response({"error": "Class name is required."}, status=status.HTTP_400_BAD_REQUEST)
        if not request.user.is_teacher:
            return Response({"error": "Only teachers can create classes."}, status=status.HTTP_403_FORBIDDEN)

        classes = Classes.objects.create(name=classname, teacher=request.user.teacher)
        serializer = ClassesSerialzer(classes)
        data = serializer.data
        data["student_count"] = classes.students.count()
        return Response({"message": "Class sucessfully created", "data": data}, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        if not (request.user.is_teacher or request.user.is_student):
            return Response({"error": "Only teachers can view classes."}, status=status.HTTP_403_FORBIDDEN)

        classes = Classes.objects.all()
        serializer = ClassesSerialzer(classes, many=True)
        data = serializer.data
        # Add student count to each class
        for idx, class_obj in enumerate(classes):
            data[idx]["student_count"] = class_obj.students.count()
        return Response(data, status=status.HTTP_200_OK)

class ClassesDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, class_id):
            classes = get_object_or_404(Classes, id=class_id)
            serializer = ClassesSerialzer(classes)
            return Response(serializer.data, status=status.HTTP_200_OK) 
    


# class EnrollStudent(APIView):
#     permission_classes = [IsAuthenticated]

#     def post(self, request):
#         student = request.user

#         if not student.is_student:
#             return Response({
#                 "message" : "Only students can register for a class"
#             })
#         classes = Classes.objects.update(User = request.user)
#         classes.save()
#         return Response({
#             "message" : "You have successfully registered to this course"
#         })
        


class EnrollStudent(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, class_id):
        user = request.user

        

        # users = Student.objects.get(user=user)  
        # print(users.is_student)

        # Check if user is a student - adjust this based on your user model
        if not hasattr(user, 'student'):
            return Response({
                "message": "Only students can register for a class."
            }, status=status.HTTP_403_FORBIDDEN)

        student = user.student  # Get the Student instance related to the user

        # class_id = request.data.get('class_id')
        if not class_id:
            return Response({"error": "class_id is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            class_instance = Classes.objects.get(id=class_id)
        except Classes.DoesNotExist:
            return Response({"error": "Class not found."}, status=status.HTTP_404_NOT_FOUND)
        
        if class_instance.students.filter(id=student.id).exists():
            return Response({
                "message": f"You are already registered to the class '{class_instance.name}'."
            }, status=status.HTTP_400_BAD_REQUEST)

        # Add student to the class's students
        class_instance.students.add(student)
        class_instance.save()

        return Response({
            "message": f"You have successfully registered to the class '{class_instance.name}'."
        }, status=status.HTTP_200_OK)


class SubjectView(APIView):
    permission_classes = {IsAuthenticated}
    serializer_class = SubjectSerializer

    def post(self, request, class_id):
        if not request.user.is_teacher:
            return Response({"error": "Only teachers can create subjects."}, status=status.HTTP_403_FORBIDDEN)

        class_instance = get_object_or_404(Classes, id=class_id)
        if class_instance.teacher != request.user.teacher:
            return Response({"error": "You can only create subjects for your own classes."}, status=status.HTTP_403_FORBIDDEN)
        # Check if the subject name already exists in the class
        subject_name = request.data.get("name")
        # subject_description = request.data.get("subject_description")
        if class_instance.subjects.filter(name=subject_name).exists():
            return Response({"error": "Subject with this name already exists in the class."}, status=status.HTTP_400_BAD_REQUEST)

        if not subject_name:
            return Response({"error": "Subject name is required."}, status=status.HTTP_400_BAD_REQUEST)
        serializer = SubjectSerializer(data=request.data)
        if serializer.is_valid():
            subject = serializer.save(teacher=request.user.teacher, classname=class_instance, )
            return Response({"message": "Subject created successfully", "data": SubjectSerializer(subject).data}, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def get(self, request, class_id):
        if not request.user.is_teacher and not request.user.is_student:
            return Response({"error": "Only teachers and students can view subjects."}, status=status.HTTP_403_FORBIDDEN)

        class_instance = get_object_or_404(Classes, id=class_id)
        subjects = class_instance.subjects.all()
        serializer = SubjectSerializer(subjects, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    


class TopicView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = TopicSerializer

    def post(self, request, subject_id):
        if not request.user.is_teacher:
            return Response({"error": "Only teachers can create topics."}, status=status.HTTP_403_FORBIDDEN)

        

        subject = get_object_or_404(Subject, id=subject_id)

        
        if subject.teacher != request.user.teacher:
            return Response({"error": "You can only create topics for your own subjects."}, status=status.HTTP_403_FORBIDDEN)

        topic_name = request.data.get("name")
        if not topic_name:
            return Response({"error": "Topic name is required."}, status=status.HTTP_400_BAD_REQUEST)

        serializer = TopicSerializer(data=request.data)
        if serializer.is_valid():
            topic = serializer.save(subject=subject)
            return Response({"message": "Topic created successfully", "data": TopicSerializer(topic).data}, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def get(self, request, subject_id):
        if not request.user.is_teacher and not request.user.is_student:
            return Response({"error": "Only teachers and students can view topics."}, status=status.HTTP_403_FORBIDDEN)

        subject = get_object_or_404(Subject, id=subject_id)
        topics = subject.topics.all()
        serializer = TopicSerializer(topics, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    




class AssignmentView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = AssignmentSerializer

    def post(self, request, subject_id):
        if not request.user.is_teacher:
            return Response({"error": "Only teachers can create assignments."}, status=status.HTTP_403_FORBIDDEN)

        subject = get_object_or_404(Subject, id=subject_id)
        if subject.teacher != request.user.teacher:
            return Response({"error": "You can only create assignments for your own subjects."}, status=status.HTTP_403_FORBIDDEN)

        serializer = AssignmentSerializer(data=request.data)
        if serializer.is_valid():
            assignment = serializer.save(subject=subject, created_by=request.user)
            return Response({"message": "Assignment created successfully", "data": AssignmentSerializer(assignment).data}, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def get(self, request, subject_id):
        if not request.user.is_teacher and not request.user.is_student:
            return Response({"error": "Only teachers and students can view assignments."}, status=status.HTTP_403_FORBIDDEN)
        

        subject = get_object_or_404(Subject, id=subject_id)
        if request.user.is_teacher and subject.teacher.user != request.user:
            return Response({"error": "You can only view assignments for your own subjects."}, status=status.HTTP_403_FORBIDDEN)
        assignments = subject.assignments.all()
        serializer = AssignmentSerializer(assignments, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    


class SubmitAssignmentView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = AssignmentSubmissionSerializer

    def post(self, request, assignment_id):
        if not request.user.is_student:
            return Response({"error": "Only students can submit assignments."}, status=status.HTTP_403_FORBIDDEN)

        assignment = get_object_or_404(Assignment, id=assignment_id)
        student = request.user.student

        # Check if the current time is past the assignment's due date
        if assignment.due_date and timezone.now() > assignment.due_date:
            return Response({"error": "Assignment submission deadline has passed."}, status=status.HTTP_400_BAD_REQUEST)

        serializer = AssignmentSubmissionSerializer(data=request.data)
        if serializer.is_valid():
            submission = serializer.save(assignment=assignment, student=student)
            return Response({"message": "Submission created successfully", "data": AssignmentSubmissionSerializer(submission).data}, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def get(self, request, assignment_id):
        if not request.user.is_teacher and not request.user.is_student:
            return Response({"error": "Only teachers and students can view submissions."}, status=status.HTTP_403_FORBIDDEN)

        assignment = get_object_or_404(Assignment, id=assignment_id)
        submissions = assignment.submissions.all()
        serializer = AssignmentSubmissionSerializer(submissions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
