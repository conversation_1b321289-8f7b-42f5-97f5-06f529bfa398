from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework.permissions import AllowAny

schema_view = get_schema_view(
    openapi.Info(title="MultiUser API", default_version='v1'),
    public=True,
    permission_classes=[AllowAny],
)

urlpatterns = [
    path('admin/', admin.site.urls),

    # New role-based API structure
    path('api/', include('core_app.urls')),
    path('api/teacher/', include('teacher_app.urls')),
    path('api/student/', include('student_app.urls')),
    # path('api/parent/', include('parent_app.urls')),  # To be implemented
    # path('api/admin/', include('admin_app.urls')),    # To be implemented

    # Legacy app (temporarily commented out)
    # path('api/legacy/', include('Ilearnova.urls')),

    # API Documentation
    path('api/swagger/', schema_view.with_ui('swagger', cache_timeout=0)),
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
