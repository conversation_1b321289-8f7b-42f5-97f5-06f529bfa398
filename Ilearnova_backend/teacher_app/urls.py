from django.urls import path
from .views import (
    TeacherAssignmentListCreateView, TeacherAssignmentDetailView,
    TeacherExaminationListCreateView, TeacherExaminationDetailView,
    AssignmentSubmissionsView, ExaminationSubmissionsView
)

app_name = 'teacher_app'

urlpatterns = [
    # Assignment management
    path('assignments/', TeacherAssignmentListCreateView.as_view(), name='assignment-list-create'),
    path('assignments/<int:pk>/', TeacherAssignmentDetailView.as_view(), name='assignment-detail'),
    path('assignments/<int:assignment_id>/submissions/', AssignmentSubmissionsView.as_view(), name='assignment-submissions'),
    
    # Examination management
    path('examinations/', TeacherExaminationListCreateView.as_view(), name='examination-list-create'),
    path('examinations/<int:pk>/', TeacherExaminationDetailView.as_view(), name='examination-detail'),
    path('examinations/<int:examination_id>/submissions/', ExaminationSubmissionsView.as_view(), name='examination-submissions'),
]
