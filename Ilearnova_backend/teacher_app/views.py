from core_app.views import TeacherPermission
from django.shortcuts import get_object_or_404
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated
from student_app.models import ExamSubmission, Submission
from student_app.serializers import ExamSubmissionSerializer, SubmissionSerializer

from .models import Assignment, Examination
from .serializers import AssignmentSerializer, ExaminationSerializer


class TeacherAssignmentListCreateView(generics.ListCreateAPIView):
    """List and create assignments for teachers"""
    serializer_class = AssignmentSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        """Filter assignments by teacher's organizations"""
        user = self.request.user
        # Get all organizations where user has teacher role
        teacher_orgs = user.get_role_organizations('teacher')
        return Assignment.objects.filter(
            organization__in=teacher_orgs,
            created_by=user
        ).order_by('-created_at')

    def perform_create(self, serializer):
        """Set the created_by field to current user"""
        serializer.save(created_by=self.request.user)


class TeacherAssignmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a specific assignment"""
    serializer_class = AssignmentSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        """Filter assignments by teacher's organizations"""
        user = self.request.user
        teacher_orgs = user.get_role_organizations('teacher')
        return Assignment.objects.filter(
            organization__in=teacher_orgs,
            created_by=user
        )


class TeacherExaminationListCreateView(generics.ListCreateAPIView):
    """List and create examinations for teachers"""
    serializer_class = ExaminationSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        """Filter examinations by teacher's organizations"""
        user = self.request.user
        teacher_orgs = user.get_role_organizations('teacher')
        return Examination.objects.filter(
            organization__in=teacher_orgs,
            created_by=user
        ).order_by('-created_at')

    def perform_create(self, serializer):
        """Set the created_by field to current user"""
        serializer.save(created_by=self.request.user)


class TeacherExaminationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a specific examination"""
    serializer_class = ExaminationSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        """Filter examinations by teacher's organizations"""
        user = self.request.user
        teacher_orgs = user.get_role_organizations('teacher')
        return Examination.objects.filter(
            organization__in=teacher_orgs,
            created_by=user
        )


class AssignmentSubmissionsView(generics.ListAPIView):
    """View all submissions for a specific assignment"""
    serializer_class = SubmissionSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        assignment_id = self.kwargs['assignment_id']
        assignment = get_object_or_404(Assignment, id=assignment_id)

        # Check if teacher has permission to view this assignment
        user = self.request.user
        if assignment.created_by != user:
            return Submission.objects.none()

        return Submission.objects.filter(assignment=assignment).order_by('-submitted_at')


class ExaminationSubmissionsView(generics.ListAPIView):
    """View all submissions for a specific examination"""
    serializer_class = ExamSubmissionSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        examination_id = self.kwargs['examination_id']
        examination = get_object_or_404(Examination, id=examination_id)

        # Check if teacher has permission to view this examination
        user = self.request.user
        if examination.created_by != user:
            return ExamSubmission.objects.none()

        return ExamSubmission.objects.filter(examination=examination).order_by('-submitted_at')
