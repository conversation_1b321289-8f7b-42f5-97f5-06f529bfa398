from core_app.models import CustomUser, Organization, Subject
from django.db import models


class Assignment(models.Model):
    """
    Represents an assignment within a subject and organization.
    Updated to support organization-scoped data.
    """
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    due_date = models.DateTimeField()
    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='assignments'
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='assignments',
        null=True,
        blank=True
    )
    created_by = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='created_assignments'
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Assignment"
        verbose_name_plural = "Assignments"
        ordering = ['organization', 'subject', 'due_date']

    def __str__(self):
        return f"{self.title} ({self.subject.name})"

    def save(self, *args, **kwargs):
        # Auto-set organization from subject
        if self.subject and not self.organization:
            self.organization = self.subject.organization
        super().save(*args, **kwargs)


class Examination(models.Model):
    """
    Represents an examination within a subject and organization.
    Updated to support organization-scoped data.
    """
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    subject = models.ForeignKey(
        Subject,
        on_delete=models.CASCADE,
        related_name='exams'
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='examinations',
        null=True,
        blank=True
    )
    created_by = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='created_exams'
    )
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Examination"
        verbose_name_plural = "Examinations"
        ordering = ['organization', 'subject', 'start_time']

    def __str__(self):
        return f"{self.title} ({self.subject.name})"

    def save(self, *args, **kwargs):
        # Auto-set organization from subject
        if self.subject and not self.organization:
            self.organization = self.subject.organization
        super().save(*args, **kwargs)
