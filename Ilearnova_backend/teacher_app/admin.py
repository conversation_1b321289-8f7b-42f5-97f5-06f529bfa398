from django.contrib import admin

from .models import Assignment, Examination


@admin.register(Assignment)
class AssignmentAdmin(admin.ModelAdmin):
    list_display = ['title', 'subject', 'organization', 'created_by', 'due_date', 'is_active']
    list_filter = ['organization', 'is_active', 'due_date', 'created_at']
    search_fields = ['title', 'subject__name', 'organization__name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Examination)
class ExaminationAdmin(admin.ModelAdmin):
    list_display = ['title', 'subject', 'organization', 'created_by', 'start_time', 'end_time', 'is_active']
    list_filter = ['organization', 'is_active', 'start_time', 'created_at']
    search_fields = ['title', 'subject__name', 'organization__name']
    readonly_fields = ['created_at', 'updated_at']
