from django.contrib import admin
from django.contrib.auth.admin import UserAdmin

from .models import (
    Classes,
    CustomUser,
    Organization,
    Subject,
    Topic,
    UserOrganizationRole,
    UserRole,
)


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code', 'email']
    readonly_fields = ['id', 'created_at', 'updated_at']


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'category', 'is_active', 'requires_verification']
    list_filter = ['category', 'is_active', 'requires_verification']
    search_fields = ['name', 'code']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ['username', 'email', 'first_name', 'last_name', 'is_verified', 'is_active']
    list_filter = ['is_verified', 'is_active', 'is_staff', 'is_superuser']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = UserAdmin.fieldsets + (
        ('Additional Info', {
            'fields': ('bio', 'profile_pic', 'phone_number', 'date_of_birth',
                      'address', 'is_verified', 'created_at', 'updated_at')
        }),
    )


@admin.register(UserOrganizationRole)
class UserOrganizationRoleAdmin(admin.ModelAdmin):
    list_display = ['user', 'organization', 'role', 'is_active', 'is_verified', 'assigned_at']
    list_filter = ['role', 'is_active', 'is_verified', 'assigned_at']
    search_fields = ['user__username', 'organization__name', 'role__name']
    readonly_fields = ['assigned_at', 'verified_at']


@admin.register(Classes)
class ClassesAdmin(admin.ModelAdmin):
    list_display = ['name', 'organization', 'teacher_role', 'is_active', 'created_at']
    list_filter = ['organization', 'is_active', 'created_at']
    search_fields = ['name', 'organization__name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'classname', 'organization', 'teacher_role', 'is_active']
    list_filter = ['organization', 'is_active', 'created_at']
    search_fields = ['name', 'classname__name', 'organization__name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Topic)
class TopicAdmin(admin.ModelAdmin):
    list_display = ['name', 'subject', 'organization', 'is_active']
    list_filter = ['organization', 'is_active', 'created_at']
    search_fields = ['name', 'subject__name']
    readonly_fields = ['created_at', 'updated_at']
