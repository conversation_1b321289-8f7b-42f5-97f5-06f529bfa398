from django.urls import path
from .views import (
    RoleBasedLoginView, TeacherRegistrationView, StudentRegistrationView,
    ParentRegistrationView, OrganizationListView, UserRoleListView,
    UserProfileView
)

app_name = 'core_app'

urlpatterns = [
    # Authentication
    path('auth/login/', RoleBasedLoginView.as_view(), name='login'),
    
    # Registration
    path('auth/register/teacher/', TeacherRegistrationView.as_view(), name='teacher-register'),
    path('auth/register/student/', StudentRegistrationView.as_view(), name='student-register'),
    path('auth/register/parent/', ParentRegistrationView.as_view(), name='parent-register'),
    
    # User Profile
    path('auth/profile/', UserProfileView.as_view(), name='user-profile'),
    
    # Public Information
    path('organizations/', OrganizationListView.as_view(), name='organization-list'),
    path('roles/', UserRoleListView.as_view(), name='role-list'),
]
