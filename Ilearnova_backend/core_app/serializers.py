from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from rest_framework import serializers

from .models import (
    Organization, UserRole, CustomUser, UserOrganizationRole,
    Classes, Subject, Topic
)

User = get_user_model()


class OrganizationSerializer(serializers.ModelSerializer):
    """Serializer for Organization model"""
    class Meta:
        model = Organization
        fields = ['id', 'name', 'code', 'description', 'address', 'phone_number',
                 'email', 'website', 'is_active', 'created_at']
        read_only_fields = ['id', 'created_at']


class UserRoleSerializer(serializers.ModelSerializer):
    """Serializer for UserRole model"""
    class Meta:
        model = UserRole
        fields = ['id', 'name', 'code', 'description', 'category', 'is_active',
                 'requires_verification', 'can_have_multiple']
        read_only_fields = ['id']


class UserOrganizationRoleSerializer(serializers.ModelSerializer):
    """Serializer for UserOrganizationRole model"""
    role = UserRoleSerializer(read_only=True)
    organization = OrganizationSerializer(read_only=True)

    class Meta:
        model = UserOrganizationRole
        fields = ['id', 'user', 'organization', 'role', 'is_active', 'is_verified',
                 'assigned_at', 'verified_at', 'additional_data']
        read_only_fields = ['id', 'assigned_at', 'verified_at']


class UserSerializer(serializers.ModelSerializer):
    """Enhanced user serializer with role information"""
    roles = UserOrganizationRoleSerializer(source='user_organization_roles', many=True, read_only=True)

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'bio',
                 'profile_pic', 'phone_number', 'date_of_birth', 'address',
                 'is_verified', 'date_joined', 'roles']
        read_only_fields = ['id', 'date_joined', 'roles']


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Base serializer for user registration"""
    password = serializers.CharField(write_only=True)
    organization_code = serializers.CharField(write_only=True)
    role_code = serializers.CharField(write_only=True)
    additional_data = serializers.JSONField(required=False, default=dict)

    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name', 'bio',
                 'phone_number', 'date_of_birth', 'address', 'password',
                 'organization_code', 'role_code', 'additional_data']
        extra_kwargs = {
            'password': {'write_only': True}
        }

    def validate_organization_code(self, value):
        """Validate that organization exists and is active"""
        try:
            organization = Organization.objects.get(code=value, is_active=True)
            return value
        except Organization.DoesNotExist:
            raise serializers.ValidationError("Invalid organization code")

    def validate_role_code(self, value):
        """Validate that role exists and is active"""
        try:
            role = UserRole.objects.get(code=value, is_active=True)
            return value
        except UserRole.DoesNotExist:
            raise serializers.ValidationError("Invalid role code")

    def create(self, validated_data):
        """Create user with organization role assignment"""
        organization_code = validated_data.pop('organization_code')
        role_code = validated_data.pop('role_code')
        additional_data = validated_data.pop('additional_data', {})
        password = validated_data.pop('password')

        # Get organization and role
        organization = Organization.objects.get(code=organization_code)
        role = UserRole.objects.get(code=role_code)

        # Create user
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()

        # Create role assignment
        role_assignment = UserOrganizationRole.objects.create(
            user=user,
            organization=organization,
            role=role,
            additional_data=additional_data
        )

        # Send verification email if required
        if role.requires_verification:
            self._send_verification_email(user, role, organization)

        return user

    def _send_verification_email(self, user, role, organization):
        """Send verification email to admin"""
        try:
            verification_link = f"http://localhost:8000/admin/core_app/userorganizationrole/{user.user_organization_roles.first().id}/change/"
            subject = f'{role.name} Verification Required'
            message = f'Please verify {user.username} ({role.name}) for {organization.name} by clicking this link: {verification_link}'
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [settings.ADMIN_EMAIL],
                fail_silently=True
            )
        except Exception:
            # Log error but don't fail registration
            pass


# Role-specific registration serializers
class TeacherRegistrationSerializer(UserRegistrationSerializer):
    """Teacher registration with additional fields"""
    id_document = serializers.FileField(required=False)

    class Meta(UserRegistrationSerializer.Meta):
        fields = UserRegistrationSerializer.Meta.fields + ['id_document']

    def create(self, validated_data):
        # Set role_code to teacher
        validated_data['role_code'] = 'teacher'

        # Extract teacher-specific data
        id_document = validated_data.pop('id_document', None)
        additional_data = validated_data.get('additional_data', {})

        if id_document:
            additional_data['id_document'] = str(id_document)

        validated_data['additional_data'] = additional_data

        return super().create(validated_data)


class StudentRegistrationSerializer(UserRegistrationSerializer):
    """Student registration with additional fields"""
    student_id = serializers.CharField(required=False)
    parent_email = serializers.EmailField(required=False)

    class Meta(UserRegistrationSerializer.Meta):
        fields = UserRegistrationSerializer.Meta.fields + ['student_id', 'parent_email']

    def create(self, validated_data):
        # Set role_code to student
        validated_data['role_code'] = 'student'

        # Extract student-specific data
        student_id = validated_data.pop('student_id', None)
        parent_email = validated_data.pop('parent_email', None)
        additional_data = validated_data.get('additional_data', {})

        if student_id:
            additional_data['student_id'] = student_id
        if parent_email:
            additional_data['parent_email'] = parent_email

        validated_data['additional_data'] = additional_data

        return super().create(validated_data)


class ParentRegistrationSerializer(UserRegistrationSerializer):
    """Parent registration with additional fields"""
    children_info = serializers.JSONField(required=False, default=list)

    class Meta(UserRegistrationSerializer.Meta):
        fields = UserRegistrationSerializer.Meta.fields + ['children_info']

    def create(self, validated_data):
        # Set role_code to parent
        validated_data['role_code'] = 'parent'

        # Extract parent-specific data
        children_info = validated_data.pop('children_info', [])
        additional_data = validated_data.get('additional_data', {})

        if children_info:
            additional_data['children_info'] = children_info

        validated_data['additional_data'] = additional_data

        return super().create(validated_data)


class ClassesSerializer(serializers.ModelSerializer):
    """Serializer for Classes model"""
    teacher_name = serializers.CharField(source='teacher_role.user.get_full_name', read_only=True)
    student_count = serializers.SerializerMethodField()
    organization_name = serializers.CharField(source='organization.name', read_only=True)

    class Meta:
        model = Classes
        fields = ['id', 'name', 'organization', 'organization_name', 'teacher_role', 
                 'teacher_name', 'description', 'is_active', 'created_at', 'student_count']
        read_only_fields = ['id', 'created_at', 'teacher_name', 'organization_name', 'student_count']

    def get_student_count(self, obj):
        return obj.student_roles.count()


class SubjectSerializer(serializers.ModelSerializer):
    """Serializer for Subject model"""
    teacher_name = serializers.CharField(source='teacher_role.user.get_full_name', read_only=True)
    class_name = serializers.CharField(source='classname.name', read_only=True)

    class Meta:
        model = Subject
        fields = ['id', 'name', 'organization', 'teacher_role', 'teacher_name',
                 'classname', 'class_name', 'description', 'is_active', 'created_at']
        read_only_fields = ['id', 'created_at', 'teacher_name', 'class_name']


class TopicSerializer(serializers.ModelSerializer):
    """Serializer for Topic model"""
    subject_name = serializers.CharField(source='subject.name', read_only=True)

    class Meta:
        model = Topic
        fields = ['id', 'name', 'subject', 'subject_name', 'organization',
                 'description', 'is_active', 'created_at']
        read_only_fields = ['id', 'created_at', 'subject_name']
