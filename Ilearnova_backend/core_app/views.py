from django.contrib.auth import authenticate
from rest_framework import generics, permissions, status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

from .models import Organization, UserRole
from .serializers import (
    OrganizationSerializer,
    ParentRegistrationSerializer,
    StudentRegistrationSerializer,
    TeacherRegistrationSerializer,
    UserRoleSerializer,
    UserSerializer,
)


class RoleBasedPermission(permissions.BasePermission):
    """
    Custom permission class that checks user roles within organizations
    """
    required_roles = []  # Override in subclasses

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        # Superusers have all permissions
        if request.user.is_superuser:
            return True

        # Check if user has any of the required roles
        if not self.required_roles:
            return True

        for role_code in self.required_roles:
            if request.user.has_role(role_code):
                return True
        return False


class TeacherPermission(RoleBasedPermission):
    required_roles = ['teacher', 'admin']


class StudentPermission(RoleBasedPermission):
    required_roles = ['student']


class AdminPermission(RoleBasedPermission):
    required_roles = ['admin', 'school_admin']


# Authentication Views
class RoleBasedLoginView(APIView):
    """Enhanced login view with role-based authentication"""
    permission_classes = [AllowAny]

    def post(self, request):
        username = request.data.get("username")
        password = request.data.get("password")
        organization_code = request.data.get("organization_code")

        if not username or not password:
            return Response(
                {"error": "Username and password are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = authenticate(username=username, password=password)

        if not user:
            return Response(
                {"error": "Invalid credentials"},
                status=status.HTTP_401_UNAUTHORIZED
            )

        # Check if user has roles in the specified organization (if provided)
        if organization_code:
            try:
                organization = Organization.objects.get(code=organization_code, is_active=True)
                user_roles = user.get_roles_in_organization(organization)
                if not user_roles.filter(is_active=True).exists():
                    return Response(
                        {"error": "User has no active roles in this organization"},
                        status=status.HTTP_403_FORBIDDEN
                    )
            except Organization.DoesNotExist:
                return Response(
                    {"error": "Invalid organization code"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Generate JWT tokens
        serializer = TokenObtainPairSerializer(data={"username": username, "password": password})
        try:
            serializer.is_valid(raise_exception=True)
            tokens = serializer.validated_data

            # Add user info and roles to response
            user_serializer = UserSerializer(user)
            response_data = {
                **tokens,
                "user": user_serializer.data
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception:
            return Response(
                {"error": "Token generation failed"},
                status=status.HTTP_400_BAD_REQUEST
            )


# Registration Views
class TeacherRegistrationView(generics.CreateAPIView):
    """Teacher registration with role-based system"""
    serializer_class = TeacherRegistrationSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                "message": "Teacher registration successful. Verification may be required.",
                "user": UserSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class StudentRegistrationView(generics.CreateAPIView):
    """Student registration with role-based system"""
    serializer_class = StudentRegistrationSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                "message": "Student registration successful.",
                "user": UserSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ParentRegistrationView(generics.CreateAPIView):
    """Parent registration with role-based system"""
    serializer_class = ParentRegistrationSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                "message": "Parent registration successful. Verification may be required.",
                "user": UserSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Organization and Role Management Views
class OrganizationListView(generics.ListAPIView):
    """List all active organizations"""
    queryset = Organization.objects.filter(is_active=True)
    serializer_class = OrganizationSerializer
    permission_classes = [AllowAny]  # Public information


class UserRoleListView(generics.ListAPIView):
    """List all active user roles"""
    queryset = UserRole.objects.filter(is_active=True)
    serializer_class = UserRoleSerializer
    permission_classes = [AllowAny]  # Public information


class UserProfileView(APIView):
    """Get and update user profile with role information"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data)

    def put(self, request):
        serializer = UserSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                "message": "Profile updated successfully",
                "user": serializer.data
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
